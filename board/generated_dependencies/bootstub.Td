obj/bootstub.panda.o: bootstub.c config.h inc/stm32f4xx.h \
 inc/stm32f413xx.h inc/core_cm4.h inc/cmsis_version.h \
 inc/cmsis_compiler.h inc/cmsis_gcc.h inc/mpu_armv7.h \
 inc/system_stm32f4xx.h obj/gitversion.h inc/stm32f4xx_hal_gpio_ex.h \
 inc/stm32f4xx_hal_def.h inc/stm32f4xx.h libc.h provision.h critical.h \
 faults.h drivers/registers.h drivers/interrupts.h drivers/clock.h \
 drivers/llgpio.h drivers/adc.h drivers/pwm.h board.h \
 board_declarations.h boards/common.h drivers/harness.h drivers/fan.h \
 drivers/rtc.h drivers/clock_source.h boards/white.h boards/grey.h \
 boards/black.h boards/uno.h boards/dos.h gpio.h drivers/spi.h \
 drivers/usb.h ../crypto/rsa.h ../crypto/stdint.h ../crypto/sha.h \
 ../crypto/hash-internal.h obj/cert.h spi_flasher.h
config.h:
inc/stm32f4xx.h:
inc/stm32f413xx.h:
inc/core_cm4.h:
inc/cmsis_version.h:
inc/cmsis_compiler.h:
inc/cmsis_gcc.h:
inc/mpu_armv7.h:
inc/system_stm32f4xx.h:
obj/gitversion.h:
inc/stm32f4xx_hal_gpio_ex.h:
inc/stm32f4xx_hal_def.h:
inc/stm32f4xx.h:
libc.h:
provision.h:
critical.h:
faults.h:
drivers/registers.h:
drivers/interrupts.h:
drivers/clock.h:
drivers/llgpio.h:
drivers/adc.h:
drivers/pwm.h:
board.h:
board_declarations.h:
boards/common.h:
drivers/harness.h:
drivers/fan.h:
drivers/rtc.h:
drivers/clock_source.h:
boards/white.h:
boards/grey.h:
boards/black.h:
boards/uno.h:
boards/dos.h:
gpio.h:
drivers/spi.h:
drivers/usb.h:
../crypto/rsa.h:
../crypto/stdint.h:
../crypto/sha.h:
../crypto/hash-internal.h:
obj/cert.h:
spi_flasher.h:
