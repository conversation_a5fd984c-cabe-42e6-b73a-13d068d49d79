﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{BD34DB24-F5DC-4992-A74F-05FAF731ABED}</ProjectGuid>
    <TemplateGuid>{a1357fe7-03e0-4d61-85f4-09c7ed38c0c1}</TemplateGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <MinimumVisualStudioVersion>12.0</MinimumVisualStudioVersion>
    <Configuration>$driverCurrentWindowsConfigurationName$ Debug</Configuration>
    <Platform Condition="'$(Platform)' == ''">Win32</Platform>
    <RootNamespace>panda_Driver_Package</RootNamespace>
    <WindowsTargetPlatformVersion>$(LatestTargetPlatformVersion)</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <ConfigurationType>Utility</ConfigurationType>
    <DriverType>Package</DriverType>
    <DisableFastUpToDateCheck>true</DisableFastUpToDateCheck>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <ConfigurationType>Utility</ConfigurationType>
    <DriverType>Package</DriverType>
    <DisableFastUpToDateCheck>true</DisableFastUpToDateCheck>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <!-- Debug the associated application, not the WinUSB driver -->
    <DebuggerFlavor>DbgengRemoteDebugger</DebuggerFlavor>
    <HardwareIdString />
    <CommandLine />
    <DeployFiles />
    <EnableVerifier>False</EnableVerifier>
    <AllDrivers>False</AllDrivers>
    <VerifyProjectOutput>True</VerifyProjectOutput>
    <VerifyDrivers />
    <VerifyFlags>133563</VerifyFlags>
    <OutDir>$(SolutionDir)$(Configuration)_$(PlatformShortName)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <!-- Debug the associated application, not the WinUSB driver -->
    <DebuggerFlavor>DbgengRemoteDebugger</DebuggerFlavor>
    <HardwareIdString />
    <CommandLine />
    <DeployFiles />
    <EnableVerifier>False</EnableVerifier>
    <AllDrivers>False</AllDrivers>
    <VerifyProjectOutput>True</VerifyProjectOutput>
    <VerifyDrivers />
    <VerifyFlags>133563</VerifyFlags>
    <OutDir>$(SolutionDir)$(Configuration)_$(PlatformShortName)\</OutDir>
  </PropertyGroup>
  <ItemGroup>
    <Inf Include="panda.inf" />
    <!-- Explicitly set all FilesToPackage since there is no dependency on a driver -->
    <FilesToPackage Include="@(Inf->'%(CopyOutput)')" Condition="'@(Inf)'!=''" />
    <FilesToPackage Condition="$(KMDF_VERSION_MAJOR) == 1 and $(KMDF_VERSION_MINOR) &lt; 13" Include="$(WDKContentRoot)redist\wdf\$(DDKPlatform)\WdfCoinstaller$(KMDF_VERSION_MAJOR_STRING)$(KMDF_VERSION_MINOR_STRING).dll" />
  </ItemGroup>
  <!-- INF handling depends on order. Some of the above ImportGroup nodes set up INF handling. -->
  <!-- Modify INF handling here. -->
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Inf>
      <!-- The WinUSB driver requires KMDF, so we must provide a KMDF version number in the INF. -->
      <!-- Use the user's KMDF setting (defaults to latest) -->
      <KmdfVersionNumber>$(KMDF_VERSION_MAJOR).$(KMDF_VERSION_MINOR)</KmdfVersionNumber>
    </Inf>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Inf>
      <!-- The WinUSB driver requires KMDF, so we must provide a KMDF version number in the INF. -->
      <!-- Use the user's KMDF setting (defaults to latest) -->
      <KmdfVersionNumber>$(KMDF_VERSION_MAJOR).$(KMDF_VERSION_MINOR)</KmdfVersionNumber>
    </Inf>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>