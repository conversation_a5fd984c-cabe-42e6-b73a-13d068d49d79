From focum information on NI hardware: https://forums.ni.com/t5/Automotive-and-Embedded-Networks/15765-2-with-NI-products/td-p/1454256

/////////////////////////////////////////////////////////////////////
Timeout Diag Command is the timeout in milliseconds the master
waits for the response to a diagnostic request message. The default is
1000 ms.

Timeout FC (Bs) is the timeout in milliseconds the master waits
for a Flow Control frame after sending a First Frame or the last
Consecutive Frame of a block. The default is 250 ms.

Timeout CF (Cr) is the timeout in milliseconds the master waits
for a Consecutive Frame in a multiframe response. The default is
250 ms.

Receive Block Size (BS) is the number of Consecutive Frames the
slave sends in one block before waiting for the next Flow Control
frame. A value of 0 (default) means all Consecutive Frames are sent
in one run without interruption.

Wait Time CF (STmin) defines the minimum time for the slave to
wait between sending two Consecutive Frames of a block. Values
from 0 to 127 are wait times in milliseconds. Values 241 to 249
(Hex F1 to F9) mean wait times of 100 μs to 900 μs, respectively.
All other values are reserved. The default is 5 ms.

Max Wait Frames (N_WFTmax) is the maximum number of WAIT
frames the master accepts before terminating the connection. The
default is 10.


There are no defined lower limits for these values; you can specify any
value down to 0. However, as you correctly pointed out, the timing is
done by Windows, and will be subject to the jitter introduced by the OS
which can easily be in the order of 10s of milliseconds. It is however
hard to give more accurate numbers as the actual jitter is dependent on
the workload of the computer
/////////////////////////////////////////////////////////////////////

J2534 04.04 does not appear to have default adjustable parameters for
the timeout related fields. For now, these default values shall be used
in the Panda J2534 implementation.
